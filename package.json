{"name": "ipcheck", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fingerprintjs/fingerprintjs-pro-react": "^2.7.0", "@types/ua-parser-js": "^0.7.39", "@vercel/functions": "^2.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "ua-parser-js": "^2.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/leaflet": "^1.9.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "typescript": "^5"}}